<?php

namespace Tests\Unit\Domain\Shared\Rules\Inventory;

use Tests\TestCase;
use App\Domain\Shared\Rules\Inventory\StockValidationRule;
use App\Domain\Shared\Rules\Inventory\InventoryRuleResult;
use App\Domain\Products\Entities\Product;
use App\Domain\Products\ValueObjects\SKU;
use App\Domain\Products\ValueObjects\Price;
use App\Domain\Products\ValueObjects\Stock;
use App\Domain\Products\ValueObjects\Weight;
use App\Domain\Products\ValueObjects\Dimensions;
use App\Domain\Products\ValueObjects\SEOData;
use App\Core\Domain\ValueObjects\Money;
use App\Enums\ProductStatus;
use Carbon\Carbon;

class StockValidationRuleTest extends TestCase
{
    private StockValidationRule $rule;
    private Product $product;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->rule = new StockValidationRule();
        
        // Test ürünü oluştur
        $this->product = new Product(
            'Test Product',
            'test-product',
            new SKU('TEST-001'),
            new Price(100.0, 'TRY'),
            new Stock(50, 5, 5, true, true), // quantity: 50, reserved: 5, lowStockThreshold: 5, tracking: true, backorder: true
            1, // category_id
            'Test Description',
            true, // status
            false // is_featured
        );
    }

    public function test_validates_sufficient_stock()
    {
        // Yeterli stok var (50 - 5 = 45 mevcut, 10 talep ediliyor)
        $result = $this->rule->applyRule($this->product, ['quantity' => 10]);

        $this->assertInstanceOf(InventoryRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isStockAvailable());
        $this->assertTrue($result->canFulfill());
        $this->assertEquals(45, $result->getAvailableQuantity());
        $this->assertEquals(5, $result->getReservedQuantity());
        $this->assertEquals(10, $result->getRequestedQuantity());
        $this->assertEquals(0, $result->getShortfall());
    }

    public function test_handles_insufficient_stock_with_backorder_allowed()
    {
        // Yetersiz stok (45 mevcut, 50 talep ediliyor) ama backorder izin veriliyor
        $this->rule->setAllowBackorder(true);
        
        $result = $this->rule->applyRule($this->product, ['quantity' => 50]);

        $this->assertInstanceOf(InventoryRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertFalse($result->isStockAvailable());
        $this->assertTrue($result->isBackorderAllowed());
        $this->assertTrue($result->canFulfill());
        $this->assertEquals(45, $result->getAvailableQuantity());
        $this->assertEquals(50, $result->getRequestedQuantity());
        $this->assertEquals(5, $result->getShortfall());
    }

    public function test_handles_insufficient_stock_without_backorder()
    {
        // Yetersiz stok ve backorder izin verilmiyor
        $this->rule->setAllowBackorder(false);
        
        $result = $this->rule->applyRule($this->product, ['quantity' => 50]);

        $this->assertInstanceOf(InventoryRuleResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isStockAvailable());
        $this->assertFalse($result->isBackorderAllowed());
        $this->assertFalse($result->canFulfill());
        $this->assertEquals(45, $result->getAvailableQuantity());
        $this->assertEquals(50, $result->getRequestedQuantity());
        $this->assertEquals(5, $result->getShortfall());
        $this->assertContains('Insufficient stock available', $result->getErrors());
    }

    public function test_not_applicable_for_digital_products()
    {
        // Dijital ürün oluştur - mock kullan
        $digitalProduct = $this->createMock(Product::class);
        $digitalProduct->method('isDigital')->willReturn(true);

        $this->assertFalse($this->rule->isApplicable($digitalProduct));
    }

    public function test_not_applicable_when_tracking_disabled()
    {
        // Stok takibi olmayan ürün - mock kullan
        $noTrackingProduct = $this->createMock(Product::class);
        $noTrackingProduct->method('isDigital')->willReturn(false);

        $mockStock = $this->createMock(Stock::class);
        $mockStock->method('isTrackingEnabled')->willReturn(false);
        $noTrackingProduct->method('getStock')->willReturn($mockStock);

        $this->assertFalse($this->rule->isApplicable($noTrackingProduct));
    }

    public function test_rule_properties()
    {
        $this->assertEquals('stock_validation', $this->rule->getName());
        $this->assertEquals(100, $this->rule->getPriority());
        $this->assertStringContainsString('stock availability', $this->rule->getDescription());
    }

    public function test_stock_critical_level_detection()
    {
        $this->rule->setMinimumStockLevel(50);
        
        // Mevcut stok (45) minimum seviyenin (50) altında
        $this->assertTrue($this->rule->isStockCritical($this->product));
        
        $warning = $this->rule->getStockWarning($this->product);
        $this->assertNotNull($warning);
        $this->assertStringContainsString('45 items remaining', $warning);
    }

    public function test_stock_not_critical()
    {
        $this->rule->setMinimumStockLevel(30);
        
        // Mevcut stok (45) minimum seviyenin (30) üstünde
        $this->assertFalse($this->rule->isStockCritical($this->product));
        
        $warning = $this->rule->getStockWarning($this->product);
        $this->assertNull($warning);
    }

    public function test_configuration_methods()
    {
        $this->rule->setAllowBackorder(true);
        $this->assertTrue($this->rule->isBackorderAllowed());
        
        $this->rule->setAllowBackorder(false);
        $this->assertFalse($this->rule->isBackorderAllowed());
        
        $this->rule->setMinimumStockLevel(25);
        $this->assertEquals(25, $this->rule->getMinimumStockLevel());
    }

    public function test_handles_non_product_entity()
    {
        // EntityInterface implement eden ama Product olmayan mock entity
        $nonProduct = $this->createMock(\App\Domain\Shared\Contracts\EntityInterface::class);

        $result = $this->rule->applyRule($nonProduct, ['quantity' => 10]);

        $this->assertInstanceOf(InventoryRuleResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->canFulfill());
        $this->assertContains('Entity is not a product', $result->getErrors());
    }

    public function test_default_quantity_when_not_specified()
    {
        // Miktar belirtilmediğinde varsayılan olarak 1 kullanılmalı
        $result = $this->rule->applyRule($this->product, []);
        
        $this->assertEquals(1, $result->getRequestedQuantity());
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->canFulfill());
    }
}
