##teamcity[testCount count='11' flowId='25108']
##teamcity[testSuiteStarted name='CLI Arguments' flowId='25108']
##teamcity[testSuiteStarted name='Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest' flowId='25108']
##teamcity[testStarted name='test_calculates_price_with_multiple_rules' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest::test_calculates_price_with_multiple_rules' flowId='25108']
##teamcity[testFinished name='test_calculates_price_with_multiple_rules' duration='38' flowId='25108']
##teamcity[testStarted name='test_calculates_price_with_tax' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest::test_calculates_price_with_tax' flowId='25108']
##teamcity[testFinished name='test_calculates_price_with_tax' duration='6' flowId='25108']
##teamcity[testStarted name='test_service_initializes_correctly' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest::test_service_initializes_correctly' flowId='25108']
##teamcity[testFinished name='test_service_initializes_correctly' duration='5' flowId='25108']
##teamcity[testStarted name='test_provides_service_statistics' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest::test_provides_service_statistics' flowId='25108']
##teamcity[testFinished name='test_provides_service_statistics' duration='5' flowId='25108']
##teamcity[testStarted name='test_health_check_passes_with_rules' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest::test_health_check_passes_with_rules' flowId='25108']
##teamcity[testFinished name='test_health_check_passes_with_rules' duration='3' flowId='25108']
##teamcity[testStarted name='test_can_unregister_rules' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest::test_can_unregister_rules' flowId='25108']
##teamcity[testFinished name='test_can_unregister_rules' duration='4' flowId='25108']
##teamcity[testStarted name='test_can_register_pricing_rules' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest::test_can_register_pricing_rules' flowId='25108']
##teamcity[testFinished name='test_can_register_pricing_rules' duration='3' flowId='25108']
##teamcity[testStarted name='test_validates_pricing' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest::test_validates_pricing' flowId='25108']
##teamcity[testFinished name='test_validates_pricing' duration='2' flowId='25108']
##teamcity[testStarted name='test_health_check_fails_without_rules' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest::test_health_check_fails_without_rules' flowId='25108']
##teamcity[testFinished name='test_health_check_fails_without_rules' duration='4' flowId='25108']
##teamcity[testStarted name='test_loads_standard_rules' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest::test_loads_standard_rules' flowId='25108']
##teamcity[testFinished name='test_loads_standard_rules' duration='2' flowId='25108']
##teamcity[testStarted name='test_calculates_price_with_quantity_discount' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest::test_calculates_price_with_quantity_discount' flowId='25108']
##teamcity[testFinished name='test_calculates_price_with_quantity_discount' duration='7' flowId='25108']
##teamcity[testSuiteFinished name='Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest' flowId='25108']
##teamcity[testSuiteFinished name='CLI Arguments' flowId='25108']
