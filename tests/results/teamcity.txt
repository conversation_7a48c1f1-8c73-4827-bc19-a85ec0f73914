##teamcity[testCount count='11' flowId='22224']
##teamcity[testSuiteStarted name='Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest' flowId='22224']
##teamcity[testStarted name='test_stock_not_critical' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest::test_stock_not_critical' flowId='22224']
##teamcity[testFinished name='test_stock_not_critical' duration='10' flowId='22224']
##teamcity[testStarted name='test_rule_properties' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest::test_rule_properties' flowId='22224']
##teamcity[testFinished name='test_rule_properties' duration='4' flowId='22224']
##teamcity[testStarted name='test_handles_insufficient_stock_without_backorder' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest::test_handles_insufficient_stock_without_backorder' flowId='22224']
##teamcity[testFinished name='test_handles_insufficient_stock_without_backorder' duration='3' flowId='22224']
##teamcity[testStarted name='test_validates_sufficient_stock' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest::test_validates_sufficient_stock' flowId='22224']
##teamcity[testFinished name='test_validates_sufficient_stock' duration='1' flowId='22224']
##teamcity[testStarted name='test_not_applicable_when_tracking_disabled' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest::test_not_applicable_when_tracking_disabled' flowId='22224']
##teamcity[testFinished name='test_not_applicable_when_tracking_disabled' duration='33' flowId='22224']
##teamcity[testStarted name='test_default_quantity_when_not_specified' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest::test_default_quantity_when_not_specified' flowId='22224']
##teamcity[testFinished name='test_default_quantity_when_not_specified' duration='2' flowId='22224']
##teamcity[testStarted name='test_handles_non_product_entity' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest::test_handles_non_product_entity' flowId='22224']
##teamcity[testFailed name='test_handles_non_product_entity' message='Failed asserting that an array contains |'Entity is not a product|'.' details='C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php:168|n' duration='10' flowId='22224']
##teamcity[testFinished name='test_handles_non_product_entity' duration='12' flowId='22224']
##teamcity[testStarted name='test_configuration_methods' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest::test_configuration_methods' flowId='22224']
##teamcity[testFinished name='test_configuration_methods' duration='1' flowId='22224']
##teamcity[testStarted name='test_stock_critical_level_detection' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest::test_stock_critical_level_detection' flowId='22224']
##teamcity[testFinished name='test_stock_critical_level_detection' duration='2' flowId='22224']
##teamcity[testStarted name='test_handles_insufficient_stock_with_backorder_allowed' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest::test_handles_insufficient_stock_with_backorder_allowed' flowId='22224']
##teamcity[testFinished name='test_handles_insufficient_stock_with_backorder_allowed' duration='2' flowId='22224']
##teamcity[testStarted name='test_not_applicable_for_digital_products' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest::test_not_applicable_for_digital_products' flowId='22224']
##teamcity[testFinished name='test_not_applicable_for_digital_products' duration='3' flowId='22224']
##teamcity[testSuiteFinished name='Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest' flowId='22224']
