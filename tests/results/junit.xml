<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php" tests="11" assertions="45" errors="0" failures="1" skipped="0" time="1.098074">
    <testcase name="test_stock_not_critical" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php" line="135" class="Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.StockValidationRuleTest" assertions="2" time="0.565288"/>
    <testcase name="test_rule_properties" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php" line="116" class="Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.StockValidationRuleTest" assertions="3" time="0.048563"/>
    <testcase name="test_handles_insufficient_stock_without_backorder" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php" line="76" class="Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.StockValidationRuleTest" assertions="9" time="0.049068"/>
    <testcase name="test_validates_sufficient_stock" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php" line="44" class="Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.StockValidationRuleTest" assertions="8" time="0.050796"/>
    <testcase name="test_not_applicable_when_tracking_disabled" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php" line="103" class="Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.StockValidationRuleTest" assertions="1" time="0.083347"/>
    <testcase name="test_default_quantity_when_not_specified" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php" line="171" class="Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.StockValidationRuleTest" assertions="3" time="0.052663"/>
    <testcase name="test_handles_non_product_entity" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php" line="158" class="Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.StockValidationRuleTest" assertions="4" time="0.058948">
      <failure type="PHPUnit\Framework\ExpectationFailedException">Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest::test_handles_non_product_entity&#13;
Failed asserting that an array contains 'Entity is not a product'.
&#13;
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php:168</failure>
    </testcase>
    <testcase name="test_configuration_methods" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php" line="146" class="Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.StockValidationRuleTest" assertions="3" time="0.042825"/>
    <testcase name="test_stock_critical_level_detection" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php" line="123" class="Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.StockValidationRuleTest" assertions="3" time="0.049190"/>
    <testcase name="test_handles_insufficient_stock_with_backorder_allowed" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php" line="59" class="Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.StockValidationRuleTest" assertions="8" time="0.048171"/>
    <testcase name="test_not_applicable_for_digital_products" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php" line="94" class="Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.StockValidationRuleTest" assertions="1" time="0.049216"/>
  </testsuite>
</testsuites>
