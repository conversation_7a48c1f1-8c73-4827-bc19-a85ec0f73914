<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="CLI Arguments" tests="11" assertions="33" errors="0" failures="0" skipped="0" time="1.238982">
    <testsuite name="Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php" tests="11" assertions="33" errors="0" failures="0" skipped="0" time="1.238982">
      <testcase name="test_calculates_price_with_quantity_discount" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php" line="54" class="Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Pricing.PricingPolicyServiceTest" assertions="4" time="0.660811"/>
      <testcase name="test_calculates_price_with_multiple_rules" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php" line="107" class="Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Pricing.PricingPolicyServiceTest" assertions="5" time="0.059371"/>
      <testcase name="test_provides_service_statistics" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php" line="223" class="Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Pricing.PricingPolicyServiceTest" assertions="4" time="0.059730"/>
      <testcase name="test_can_unregister_rules" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php" line="180" class="Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Pricing.PricingPolicyServiceTest" assertions="3" time="0.051854"/>
      <testcase name="test_can_register_pricing_rules" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php" line="39" class="Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Pricing.PricingPolicyServiceTest" assertions="1" time="0.055866"/>
      <testcase name="test_service_initializes_correctly" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php" line="28" class="Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Pricing.PricingPolicyServiceTest" assertions="4" time="0.069263"/>
      <testcase name="test_validates_pricing" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php" line="139" class="Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Pricing.PricingPolicyServiceTest" assertions="4" time="0.062471"/>
      <testcase name="test_calculates_price_with_tax" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php" line="82" class="Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Pricing.PricingPolicyServiceTest" assertions="4" time="0.060003"/>
      <testcase name="test_health_check_fails_without_rules" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php" line="214" class="Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Pricing.PricingPolicyServiceTest" assertions="1" time="0.051262"/>
      <testcase name="test_health_check_passes_with_rules" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php" line="202" class="Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Pricing.PricingPolicyServiceTest" assertions="1" time="0.051931"/>
      <testcase name="test_loads_standard_rules" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php" line="164" class="Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Pricing.PricingPolicyServiceTest" assertions="2" time="0.056420"/>
    </testsuite>
  </testsuite>
</testsuites>
