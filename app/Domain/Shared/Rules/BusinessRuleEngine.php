<?php

namespace App\Domain\Shared\Rules;

use App\Domain\Shared\Contracts\EntityInterface;

/**
 * BusinessRuleEngine
 * Yeni business rule interface'leri için rule engine
 */
class BusinessRuleEngine
{
    private array $rules = [];
    private array $executionHistory = [];

    /**
     * <PERSON>ral ekle
     */
    public function addRule($rule): void
    {
        $this->rules[] = $rule;
    }

    /**
     * Kural kaldır
     */
    public function removeRule($ruleToRemove): void
    {
        $this->rules = array_filter($this->rules, function($rule) use ($ruleToRemove) {
            return $rule !== $ruleToRemove;
        });
        
        // Array'i yeniden index'le
        $this->rules = array_values($this->rules);
    }

    /**
     * Tüm geçerli kuralları çalıştır
     */
    public function executeRules(EntityInterface $entity, array $context = []): array
    {
        $results = [];
        
        // Kuralları öncelik sırasına göre sırala
        $sortedRules = $this->sortRulesByPriority();
        
        foreach ($sortedRules as $rule) {
            if ($rule->isApplicable($entity, $context)) {
                $startTime = microtime(true);
                
                try {
                    $result = $rule->applyRule($entity, $context);
                    $duration = microtime(true) - $startTime;
                    
                    $this->recordExecution($rule, $result, $duration);
                    $results[] = $result;
                    
                } catch (\Exception $e) {
                    $duration = microtime(true) - $startTime;
                    
                    // Hata durumunda da kayıt tut
                    $this->recordExecution($rule, null, $duration, $e);
                    
                    // Hata sonucunu oluştur
                    $errorResult = $this->createErrorResult($rule, $e);
                    $results[] = $errorResult;
                }
            }
        }
        
        return $results;
    }

    /**
     * Belirli bir kural türü için kuralları çalıştır
     */
    public function executeRulesOfType(string $ruleType, EntityInterface $entity, array $context = []): array
    {
        $results = [];
        
        foreach ($this->rules as $rule) {
            if ($rule instanceof $ruleType && $rule->isApplicable($entity, $context)) {
                $result = $rule->applyRule($entity, $context);
                $results[] = $result;
            }
        }
        
        return $results;
    }

    /**
     * Kuralları öncelik sırasına göre sırala
     */
    private function sortRulesByPriority(): array
    {
        $rules = $this->rules;
        
        usort($rules, function($a, $b) {
            return $a->getPriority() <=> $b->getPriority();
        });
        
        return $rules;
    }

    /**
     * Kural çalıştırma kaydı tut
     */
    private function recordExecution($rule, $result, float $duration, ?\Exception $exception = null): void
    {
        $this->executionHistory[] = [
            'rule_name' => $rule->getName(),
            'rule_class' => get_class($rule),
            'duration' => $duration,
            'success' => $exception === null,
            'result_valid' => $result ? $result->isValid() : false,
            'exception' => $exception ? $exception->getMessage() : null,
            'timestamp' => time(),
        ];
        
        // Geçmiş kayıtlarını sınırla (son 1000 kayıt)
        if (count($this->executionHistory) > 1000) {
            $this->executionHistory = array_slice($this->executionHistory, -1000);
        }
    }

    /**
     * Hata sonucu oluştur
     */
    private function createErrorResult($rule, \Exception $exception): BusinessRuleResult
    {
        return new BusinessRuleResult(
            false,
            $rule->getName(),
            ["Rule execution failed: " . $exception->getMessage()],
            [],
            [
                'exception_type' => get_class($exception),
                'exception_message' => $exception->getMessage(),
                'rule_class' => get_class($rule),
            ]
        );
    }

    /**
     * Tüm kuralları getir
     */
    public function getRules(): array
    {
        return $this->rules;
    }

    /**
     * Kural sayısını getir
     */
    public function getRuleCount(): int
    {
        return count($this->rules);
    }

    /**
     * Belirli türdeki kuralları getir
     */
    public function getRulesOfType(string $ruleType): array
    {
        return array_filter($this->rules, function($rule) use ($ruleType) {
            return $rule instanceof $ruleType;
        });
    }

    /**
     * Engine istatistiklerini getir
     */
    public function getStatistics(): array
    {
        $totalExecutions = count($this->executionHistory);
        $successfulExecutions = count(array_filter($this->executionHistory, fn($h) => $h['success']));
        $failedExecutions = $totalExecutions - $successfulExecutions;
        
        $avgDuration = 0;
        if ($totalExecutions > 0) {
            $totalDuration = array_sum(array_column($this->executionHistory, 'duration'));
            $avgDuration = $totalDuration / $totalExecutions;
        }
        
        return [
            'total_rules' => count($this->rules),
            'total_executions' => $totalExecutions,
            'successful_executions' => $successfulExecutions,
            'failed_executions' => $failedExecutions,
            'success_rate' => $totalExecutions > 0 ? ($successfulExecutions / $totalExecutions) * 100 : 0,
            'average_duration_ms' => round($avgDuration * 1000, 2),
            'rule_types' => $this->getRuleTypeStatistics(),
        ];
    }

    /**
     * Kural türü istatistiklerini getir
     */
    private function getRuleTypeStatistics(): array
    {
        $typeStats = [];
        
        foreach ($this->rules as $rule) {
            $type = get_class($rule);
            if (!isset($typeStats[$type])) {
                $typeStats[$type] = 0;
            }
            $typeStats[$type]++;
        }
        
        return $typeStats;
    }

    /**
     * Çalıştırma geçmişini getir
     */
    public function getExecutionHistory(int $limit = 100): array
    {
        return array_slice($this->executionHistory, -$limit);
    }

    /**
     * Engine'i temizle
     */
    public function clear(): void
    {
        $this->rules = [];
        $this->executionHistory = [];
    }

    /**
     * Engine sağlık kontrolü
     */
    public function healthCheck(): array
    {
        $issues = [];
        
        if (empty($this->rules)) {
            $issues[] = 'No rules registered';
        }
        
        // Son 10 çalıştırmada %50'den fazla hata varsa
        $recentHistory = array_slice($this->executionHistory, -10);
        if (count($recentHistory) >= 5) {
            $recentFailures = count(array_filter($recentHistory, fn($h) => !$h['success']));
            if ($recentFailures / count($recentHistory) > 0.5) {
                $issues[] = 'High failure rate in recent executions';
            }
        }
        
        return [
            'healthy' => empty($issues),
            'issues' => $issues,
            'rule_count' => count($this->rules),
            'recent_executions' => count($recentHistory),
        ];
    }

    /**
     * Debug bilgilerini getir
     */
    public function getDebugInfo(): array
    {
        return [
            'rules' => array_map(function($rule) {
                return [
                    'name' => $rule->getName(),
                    'class' => get_class($rule),
                    'priority' => $rule->getPriority(),
                    'description' => $rule->getDescription(),
                ];
            }, $this->rules),
            'execution_history' => $this->getExecutionHistory(20),
            'statistics' => $this->getStatistics(),
        ];
    }
}
